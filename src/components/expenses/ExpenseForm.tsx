'use client';

import React, { useState, useEffect } from 'react';
import { useForm, type SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';

// API imports
import { useGetVendorsQuery } from '@/redux/services/vendorsApi';
import { useGetAccountsQuery } from '@/redux/services/chartOfAccountsApi';
import { useGetExpenseCategoriesByBranchQuery } from '@/redux/services/expenseCategoriesApi';
import { useGetAssetsQuery } from '@/redux/services/assetsApi';
import { useGetEmployeesQuery } from '@/redux/services/employeesApi';
import { useGetBankAccountsQuery } from '@/redux/services/bankingApi';
import { useGetBillsQuery } from '@/redux/services/billsApi';
import { useCreateExpenseMutation, useUpdateExpenseMutation, type Expense } from '@/redux/services/expensesApi';

const expenseSchema = z.object({
  // Basic fields
  description: z.string().min(1, 'Description is required'),
  amount: z.number().min(0.01, 'Amount must be greater than 0'),
  expenseDate: z.string().min(1, 'Expense date is required'),
  
  // Optional basic fields
  vendorId: z.string().optional(),
  accountId: z.string().min(1, 'Account is required'),
  receiptUrl: z.string().optional(),
  notes: z.string().optional(),
  
  // Enhanced relationship fields
  billId: z.string().optional(),
  assetId: z.string().optional(),
  employeeId: z.string().optional(),
  purchaseOrderId: z.string().optional(),
  projectId: z.string().optional(),
  costCenter: z.string().optional(),
  department: z.string().optional(),
  expenseCategoryId: z.string().optional(),
  
  // Payment fields
  paymentMethod: z.string().optional(),
  bankAccountId: z.string().optional(),
  
  // Financial breakdown
  subTotal: z.number().min(0, 'Subtotal must be non-negative'),
  taxAmount: z.number().min(0, 'Tax amount must be non-negative'),
  discountAmount: z.number().min(0, 'Discount amount must be non-negative'),
  
  // Recurring
  isRecurring: z.boolean(),

  // Status and approval
  status: z.enum(['PENDING', 'APPROVED', 'REJECTED', 'PAID']),
  approvedBy: z.string().optional(),
  approvedAt: z.string().optional(),
  paidAt: z.string().optional(),
});

type ExpenseFormData = z.infer<typeof expenseSchema>;

interface ExpenseFormProps {
  expense?: Expense;
  branchId: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function ExpenseForm({ expense, branchId, onSuccess, onCancel }: ExpenseFormProps) {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('basic');

  // API queries
  const { data: vendors } = useGetVendorsQuery();
  const { data: accounts } = useGetAccountsQuery();
  const { data: expenseCategories } = useGetExpenseCategoriesByBranchQuery(branchId);
  const { data: assets } = useGetAssetsQuery();
  const { data: employees } = useGetEmployeesQuery();
  const { data: bankAccounts } = useGetBankAccountsQuery();
  const { data: bills } = useGetBillsQuery();

  // Mutations
  const [createExpense, { isLoading: isCreating }] = useCreateExpenseMutation();
  const [updateExpense, { isLoading: isUpdating }] = useUpdateExpenseMutation();

  const isLoading = isCreating || isUpdating;

  const form = useForm<ExpenseFormData>({
    resolver: zodResolver(expenseSchema),
    mode: 'onChange',
    defaultValues: {
      description: '',
      amount: 0,
      expenseDate: new Date().toISOString().split('T')[0],
      accountId: '',
      subTotal: 0,
      taxAmount: 0,
      discountAmount: 0,
      isRecurring: false,
      status: 'PENDING',
    },
  });

  // Watch form values for controlled components
  const formValues = form.watch();

  // Populate form when editing
  useEffect(() => {
    if (expense) {
      form.reset({
        description: expense.description,
        amount: expense.amount,
        expenseDate: expense.date.split('T')[0],
        vendorId: expense.vendor_id || undefined,
        accountId: expense.vendor_id || undefined, // Note: Expense type doesn't have account_id, using vendor_id for now
        receiptUrl: expense.receipt_url || undefined,
        notes: undefined, // Note: Expense type doesn't have notes field
        billId: expense.bill_id || undefined,
        assetId: expense.asset_id || undefined,
        employeeId: expense.employee_id || undefined,
        purchaseOrderId: expense.purchase_order_id || undefined,
        projectId: expense.project_id || undefined,
        costCenter: expense.cost_center || undefined,
        department: expense.department || undefined,
        expenseCategoryId: expense.expense_category_id || undefined,
        paymentMethod: expense.payment_method || undefined,
        bankAccountId: expense.bank_account_id || undefined,
        subTotal: expense.sub_total,
        taxAmount: expense.tax_amount,
        discountAmount: expense.discount_amount,
        isRecurring: expense.is_recurring,
        status: expense.status,
        approvedBy: expense.approved_by || undefined,
        approvedAt: expense.approved_at || undefined,
        paidAt: expense.paid_at || undefined,
      });
    }
  }, [expense, form]);

  // Auto-calculate amount when subtotal, tax, or discount changes
  useEffect(() => {
    const subTotal = formValues.subTotal || 0;
    const taxAmount = formValues.taxAmount || 0;
    const discountAmount = formValues.discountAmount || 0;
    const currentAmount = formValues.amount || 0;

    const calculatedAmount = subTotal + taxAmount - discountAmount;
    if (calculatedAmount >= 0 && calculatedAmount !== currentAmount) {
      form.setValue('amount', calculatedAmount, { shouldValidate: false });
    }
  }, [formValues.subTotal, formValues.taxAmount, formValues.discountAmount, formValues.amount, form]);

  const onSubmit: SubmitHandler<ExpenseFormData> = async (data) => {
    try {
      if (expense) {
        // For updates, we can send the full data
        const updateData = {
          ...data,
          date: data.expenseDate,
        };
        await updateExpense({ id: expense.id, body: updateData }).unwrap();
        toast({
          title: 'Success',
          description: 'Expense updated successfully',
        });
      } else {
        // For creation, match the CreateExpenseRequest interface
        const createData = {
          date: data.expenseDate,
          vendor_id: data.vendorId || '',
          category: data.expenseCategoryId || 'other',
          description: data.description,
          amount: data.amount,
          payment_method: data.paymentMethod || 'cash',
          receipt_url: data.receiptUrl,
        };
        await createExpense(createData).unwrap();
        toast({
          title: 'Success',
          description: 'Expense created successfully',
        });
      }

      onSuccess?.();
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error?.data?.error || 'Failed to save expense',
        variant: 'destructive',
      });
    }
  };

  return (
    <Card className="w-full max-w-5xl mx-auto shadow-lg">
      <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
        <CardTitle className="text-2xl font-bold text-gray-800">
          {expense ? 'Edit Expense' : 'Create New Expense'}
        </CardTitle>
        <p className="text-sm text-gray-600 mt-1">
          {expense ? 'Update expense details and relationships' : 'Enter expense information across multiple categories'}
        </p>
      </CardHeader>
      <CardContent className="p-6">
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-5 h-12 bg-gray-100 p-1 rounded-lg">
              <TabsTrigger
                value="basic"
                className="data-[state=active]:bg-white data-[state=active]:shadow-sm data-[state=active]:text-blue-600 font-medium transition-all duration-200"
              >
                <div className="flex flex-col items-center space-y-1">
                  <span className="text-xs">📝</span>
                  <span className="text-xs">Basic Info</span>
                </div>
              </TabsTrigger>
              <TabsTrigger
                value="relationships"
                className="data-[state=active]:bg-white data-[state=active]:shadow-sm data-[state=active]:text-blue-600 font-medium transition-all duration-200"
              >
                <div className="flex flex-col items-center space-y-1">
                  <span className="text-xs">🔗</span>
                  <span className="text-xs">Relationships</span>
                </div>
              </TabsTrigger>
              <TabsTrigger
                value="payment"
                className="data-[state=active]:bg-white data-[state=active]:shadow-sm data-[state=active]:text-blue-600 font-medium transition-all duration-200"
              >
                <div className="flex flex-col items-center space-y-1">
                  <span className="text-xs">💳</span>
                  <span className="text-xs">Payment</span>
                </div>
              </TabsTrigger>
              <TabsTrigger
                value="financial"
                className="data-[state=active]:bg-white data-[state=active]:shadow-sm data-[state=active]:text-blue-600 font-medium transition-all duration-200"
              >
                <div className="flex flex-col items-center space-y-1">
                  <span className="text-xs">💰</span>
                  <span className="text-xs">Financial</span>
                </div>
              </TabsTrigger>
              <TabsTrigger
                value="approval"
                className="data-[state=active]:bg-white data-[state=active]:shadow-sm data-[state=active]:text-blue-600 font-medium transition-all duration-200"
              >
                <div className="flex flex-col items-center space-y-1">
                  <span className="text-xs">✅</span>
                  <span className="text-xs">Approval</span>
                </div>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="mt-6 space-y-6">
              <div className="bg-white border rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <span className="mr-2">📝</span>
                  Basic Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="description">Description *</Label>
                    <Input
                      id="description"
                      {...form.register('description')}
                      placeholder="Enter expense description"
                    />
                    {form.formState.errors.description && (
                      <p className="text-sm text-red-500 mt-1">
                        {form.formState.errors.description.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="expenseDate">Date *</Label>
                    <Input
                      id="expenseDate"
                      type="date"
                      {...form.register('expenseDate')}
                    />
                    {form.formState.errors.expenseDate && (
                      <p className="text-sm text-red-500 mt-1">
                        {form.formState.errors.expenseDate.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="amount">Amount *</Label>
                    <Input
                      id="amount"
                      type="number"
                      step="0.01"
                      {...form.register('amount', { valueAsNumber: true })}
                      placeholder="0.00"
                    />
                    {form.formState.errors.amount && (
                      <p className="text-sm text-red-500 mt-1">
                        {form.formState.errors.amount.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="vendorId">Vendor</Label>
                    <Select
                      value={formValues.vendorId || ''}
                      onValueChange={(value) => form.setValue('vendorId', value || undefined)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select vendor" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">No vendor</SelectItem>
                        {vendors?.data?.map((vendor) => (
                          <SelectItem key={vendor.id} value={vendor.id}>
                            {vendor.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="accountId">Account *</Label>
                    <Select
                      value={formValues.accountId || ''}
                      onValueChange={(value) => form.setValue('accountId', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select account" />
                      </SelectTrigger>
                      <SelectContent>
                        {accounts?.data?.map((account) => (
                          <SelectItem key={account.id} value={account.id}>
                            {account.account_code} - {account.account_name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {form.formState.errors.accountId && (
                      <p className="text-sm text-red-500 mt-1">
                        {form.formState.errors.accountId.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="expenseCategoryId">Category</Label>
                    <Select
                      value={formValues.expenseCategoryId || ''}
                      onValueChange={(value) => form.setValue('expenseCategoryId', value || undefined)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">No category</SelectItem>
                        {expenseCategories?.data?.map((category) => (
                          <SelectItem key={category.id} value={category.id}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="receiptUrl">Receipt URL</Label>
                    <Input
                      id="receiptUrl"
                      {...form.register('receiptUrl')}
                      placeholder="Enter receipt URL"
                    />
                  </div>
                </div>
              </div>

              <div className="bg-white border rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Additional Details</h3>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="notes">Notes</Label>
                    <Textarea
                      id="notes"
                      {...form.register('notes')}
                      placeholder="Additional notes"
                      rows={3}
                      className="mt-1"
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="isRecurring"
                      checked={formValues.isRecurring}
                      onCheckedChange={(checked) => form.setValue('isRecurring', !!checked)}
                    />
                    <Label htmlFor="isRecurring" className="text-sm font-medium">
                      This is a recurring expense
                    </Label>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="relationships" className="mt-6 space-y-6">
              <div className="bg-white border rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <span className="mr-2">🔗</span>
                  Entity Relationships
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="assetId">Asset</Label>
                  <Select
                    value={formValues.assetId || ''}
                    onValueChange={(value) => form.setValue('assetId', value || undefined)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select asset" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">No asset</SelectItem>
                      {assets?.assets?.map((asset) => (
                        <SelectItem key={asset.id} value={asset.id}>
                          {asset.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="employeeId">Employee</Label>
                  <Select
                    value={formValues.employeeId || ''}
                    onValueChange={(value) => form.setValue('employeeId', value || undefined)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select employee" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">No employee</SelectItem>
                      {employees?.employees?.map((employee: any) => (
                        <SelectItem key={employee.id || employee.employee_id} value={employee.id || employee.employee_id}>
                          {employee.firstName && employee.lastName
                            ? `${employee.firstName} ${employee.lastName}`
                            : employee.employee_name || employee.fullName || 'Unknown Employee'
                          }
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="billId">Related Bill</Label>
                  <Select
                    value={formValues.billId || ''}
                    onValueChange={(value) => form.setValue('billId', value || undefined)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select bill" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">No bill</SelectItem>
                      {bills?.bills?.map((bill) => (
                        <SelectItem key={bill.id} value={bill.id}>
                          {bill.billNumber || bill.id} - ${bill.totalAmount}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              </div>

              <div className="bg-white border rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <span className="mr-2">🏢</span>
                  Project & Organization
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="projectId">Project ID</Label>
                    <Input
                      id="projectId"
                      {...form.register('projectId')}
                      placeholder="Enter project ID"
                    />
                  </div>

                  <div>
                    <Label htmlFor="costCenter">Cost Center</Label>
                    <Input
                      id="costCenter"
                      {...form.register('costCenter')}
                      placeholder="Enter cost center"
                    />
                  </div>

                  <div>
                    <Label htmlFor="department">Department</Label>
                    <Input
                      id="department"
                      {...form.register('department')}
                      placeholder="Enter department"
                    />
                  </div>

                  <div>
                    <Label htmlFor="purchaseOrderId">Purchase Order ID</Label>
                    <Input
                      id="purchaseOrderId"
                      {...form.register('purchaseOrderId')}
                      placeholder="Enter PO ID"
                    />
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="payment" className="mt-6 space-y-6">
              <div className="bg-white border rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <span className="mr-2">💳</span>
                  Payment Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="paymentMethod">Payment Method</Label>
                    <Select
                      value={formValues.paymentMethod || ''}
                      onValueChange={(value) => form.setValue('paymentMethod', value || undefined)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select payment method" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">Not specified</SelectItem>
                        <SelectItem value="cash">💵 Cash</SelectItem>
                        <SelectItem value="credit_card">💳 Credit Card</SelectItem>
                        <SelectItem value="debit_card">💳 Debit Card</SelectItem>
                        <SelectItem value="bank_transfer">🏦 Bank Transfer</SelectItem>
                        <SelectItem value="check">📝 Check</SelectItem>
                        <SelectItem value="online_payment">🌐 Online Payment</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="bankAccountId">Bank Account</Label>
                    <Select
                      value={formValues.bankAccountId || ''}
                      onValueChange={(value) => form.setValue('bankAccountId', value || undefined)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select bank account" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">No bank account</SelectItem>
                        {bankAccounts?.accounts?.map((account) => (
                          <SelectItem key={account.id} value={account.id}>
                            {account.account_name} - {account.account_number}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="financial" className="mt-6 space-y-6">
              <div className="bg-white border rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <span className="mr-2">💰</span>
                  Financial Breakdown
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <Label htmlFor="subTotal">Subtotal</Label>
                    <Input
                      id="subTotal"
                      type="number"
                      step="0.01"
                      {...form.register('subTotal', { valueAsNumber: true })}
                      placeholder="0.00"
                      className="text-right"
                    />
                    {form.formState.errors.subTotal && (
                      <p className="text-sm text-red-500 mt-1">
                        {form.formState.errors.subTotal.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="taxAmount">Tax Amount</Label>
                    <Input
                      id="taxAmount"
                      type="number"
                      step="0.01"
                      {...form.register('taxAmount', { valueAsNumber: true })}
                      placeholder="0.00"
                      className="text-right"
                    />
                    {form.formState.errors.taxAmount && (
                      <p className="text-sm text-red-500 mt-1">
                        {form.formState.errors.taxAmount.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="discountAmount">Discount Amount</Label>
                    <Input
                      id="discountAmount"
                      type="number"
                      step="0.01"
                      {...form.register('discountAmount', { valueAsNumber: true })}
                      placeholder="0.00"
                      className="text-right"
                    />
                    {form.formState.errors.discountAmount && (
                      <p className="text-sm text-red-500 mt-1">
                        {form.formState.errors.discountAmount.message}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 p-6 rounded-lg">
                <div className="flex justify-between items-center text-xl font-bold text-green-800">
                  <span>Total Amount:</span>
                  <span className="text-2xl">${formValues.amount?.toFixed(2) || '0.00'}</span>
                </div>
                <p className="text-sm text-green-700 mt-2 flex items-center">
                  <span className="mr-2">📊</span>
                  Calculated as: Subtotal + Tax - Discount
                </p>
              </div>
            </TabsContent>

            <TabsContent value="approval" className="mt-6 space-y-6">
              <div className="bg-white border rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <span className="mr-2">✅</span>
                  Status & Approval
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="status">Status</Label>
                    <Select
                      value={formValues.status || 'PENDING'}
                      onValueChange={(value) => form.setValue('status', value as any)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="PENDING">⏳ Pending</SelectItem>
                        <SelectItem value="APPROVED">✅ Approved</SelectItem>
                        <SelectItem value="REJECTED">❌ Rejected</SelectItem>
                        <SelectItem value="PAID">💰 Paid</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="approvedBy">Approved By (User ID)</Label>
                    <Input
                      id="approvedBy"
                      {...form.register('approvedBy')}
                      placeholder="Enter approver user ID"
                      disabled={formValues.status !== 'APPROVED'}
                    />
                  </div>

                  <div>
                    <Label htmlFor="approvedAt">Approved At</Label>
                    <Input
                      id="approvedAt"
                      type="datetime-local"
                      {...form.register('approvedAt')}
                      disabled={formValues.status !== 'APPROVED'}
                    />
                  </div>

                  <div>
                    <Label htmlFor="paidAt">Paid At</Label>
                    <Input
                      id="paidAt"
                      type="datetime-local"
                      {...form.register('paidAt')}
                      disabled={formValues.status !== 'PAID'}
                    />
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 p-6 rounded-lg">
                <h4 className="font-semibold text-blue-900 mb-3 flex items-center">
                  <span className="mr-2">🔄</span>
                  Approval Workflow
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
                  <div className="flex items-start space-x-2">
                    <span className="text-yellow-500">⏳</span>
                    <div>
                      <strong>Pending:</strong> Expense is awaiting approval
                    </div>
                  </div>
                  <div className="flex items-start space-x-2">
                    <span className="text-green-500">✅</span>
                    <div>
                      <strong>Approved:</strong> Expense has been approved and can be paid
                    </div>
                  </div>
                  <div className="flex items-start space-x-2">
                    <span className="text-red-500">❌</span>
                    <div>
                      <strong>Rejected:</strong> Expense has been rejected
                    </div>
                  </div>
                  <div className="flex items-start space-x-2">
                    <span className="text-green-600">💰</span>
                    <div>
                      <strong>Paid:</strong> Expense has been paid
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <div className="flex justify-between items-center pt-6 border-t border-gray-200 bg-gray-50 -mx-6 -mb-6 px-6 py-4 rounded-b-lg">
            <div className="text-sm text-gray-600">
              <span className="font-medium">Current Tab:</span> {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)}
            </div>
            <div className="flex space-x-3">
              <Button type="button" variant="outline" onClick={onCancel} className="px-6">
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading} className="px-6 bg-blue-600 hover:bg-blue-700">
                {isLoading ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Saving...</span>
                  </div>
                ) : (
                  expense ? 'Update Expense' : 'Create Expense'
                )}
              </Button>
            </div>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}

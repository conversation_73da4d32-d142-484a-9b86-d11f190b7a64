'use client';

import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

import { useToast } from '@/hooks/use-toast';
import { Receipt } from 'lucide-react';

// API imports
import { useGetVendorsQuery } from '@/redux/services/vendorsApi';
import { useGetExpenseCategoriesByBranchQuery } from '@/redux/services/expenseCategoriesApi';
import { useCreateExpenseMutation, useUpdateExpenseMutation, type Expense } from '@/redux/services/expensesApi';
import { useGetAccountsQuery } from '@/redux/services/chartOfAccountsApi';
import { useGetAssetsQuery } from '@/redux/services/assetsApi';
import { useGetEmployeesQuery } from '@/redux/services/employeesApi';
import { useGetBankAccountsQuery } from '@/redux/services/bankingApi';
import { useGetBillsQuery } from '@/redux/services/billsApi';

// Complete schema with all relationship fields
const expenseSchema = z.object({
  // Required fields (matching CreateExpenseRequest)
  description: z.string().min(1, 'Description is required'),
  amount: z.number().min(0.01, 'Amount must be greater than 0'),
  expenseDate: z.string().min(1, 'Expense date is required'),
  vendorId: z.string().min(1, 'Vendor is required'),
  category: z.string().min(1, 'Category is required'),
  paymentMethod: z.enum(['cash', 'credit_card', 'debit_card', 'bank_transfer', 'check', 'online_payment'], {
    required_error: 'Payment method is required',
  }),

  // Optional relationship fields
  accountId: z.string().optional(),
  assetId: z.string().optional(),
  employeeId: z.string().optional(),
  billId: z.string().optional(),
  bankAccountId: z.string().optional(),

  // Project & Organization fields
  projectId: z.string().optional(),
  costCenter: z.string().optional(),
  department: z.string().optional(),
  purchaseOrderId: z.string().optional(),

  // Financial breakdown fields
  subTotal: z.number().optional(),
  taxAmount: z.number().optional(),
  discountAmount: z.number().optional(),

  // Additional fields
  receiptUrl: z.string().url('Invalid URL').optional().or(z.literal('')),
  notes: z.string().optional(),
  isRecurring: z.boolean().optional(),

  // Status and approval fields (for editing)
  status: z.enum(['PENDING', 'APPROVED', 'REJECTED', 'PAID']),
  approvedBy: z.string().optional(),
  approvedAt: z.string().optional(),
  paidAt: z.string().optional(),
});

type ExpenseFormData = z.infer<typeof expenseSchema>;

interface ExpenseFormProps {
  expense?: Expense;
  branchId: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function ExpenseForm({ expense, branchId, onSuccess, onCancel }: ExpenseFormProps) {
  const { toast } = useToast();

  // API queries for all relationships
  const { data: vendors } = useGetVendorsQuery();
  const { data: expenseCategories } = useGetExpenseCategoriesByBranchQuery(branchId);
  const { data: accounts } = useGetAccountsQuery();
  const { data: assets } = useGetAssetsQuery();
  const { data: employees } = useGetEmployeesQuery();
  const { data: bankAccounts } = useGetBankAccountsQuery();
  const { data: bills } = useGetBillsQuery();

  // Mutations
  const [createExpense, { isLoading: isCreating }] = useCreateExpenseMutation();
  const [updateExpense, { isLoading: isUpdating }] = useUpdateExpenseMutation();

  const isLoading = isCreating || isUpdating;

  const form = useForm<ExpenseFormData>({
    resolver: zodResolver(expenseSchema),
    mode: 'onChange',
    defaultValues: {
      description: '',
      amount: 0,
      expenseDate: new Date().toISOString().split('T')[0],
      vendorId: '',
      category: '',
      paymentMethod: 'cash' as const,

      // Relationship fields
      accountId: '',
      assetId: '',
      employeeId: '',
      billId: '',
      bankAccountId: '',

      // Project & Organization fields
      projectId: '',
      costCenter: '',
      department: '',
      purchaseOrderId: '',

      // Financial breakdown fields
      subTotal: 0,
      taxAmount: 0,
      discountAmount: 0,

      // Additional fields
      receiptUrl: '',
      notes: '',
      isRecurring: false,

      // Status and approval fields
      status: 'PENDING' as const,
      approvedBy: '',
      approvedAt: '',
      paidAt: '',
    },
  });

  // Populate form when editing
  useEffect(() => {
    if (expense) {
      form.reset({
        description: expense.description,
        amount: expense.amount,
        expenseDate: expense.date.split('T')[0],
        vendorId: expense.vendor_id || '',
        category: expense.category || '',
        paymentMethod: (expense.payment_method as any) || 'cash',
        receiptUrl: expense.receipt_url || '',
        notes: '', // Note: Expense type doesn't have notes field
        status: expense.status,
      });
    }
  }, [expense, form]);

  const onSubmit = async (data: ExpenseFormData) => {
    try {
      if (expense) {
        // For updates, we can send the full data
        const updateData = {
          ...data,
          date: data.expenseDate,
        };
        await updateExpense({ id: expense.id, body: updateData }).unwrap();
        toast({
          title: 'Success',
          description: 'Expense updated successfully',
        });
      } else {
        // For creation, match the CreateExpenseRequest interface
        const createData = {
          date: data.expenseDate,
          vendor_id: data.vendorId,
          category: data.category,
          description: data.description,
          amount: data.amount,
          payment_method: data.paymentMethod,
          receipt_url: data.receiptUrl || undefined,
        };
        await createExpense(createData).unwrap();
        toast({
          title: 'Success',
          description: 'Expense created successfully',
        });
      }

      onSuccess?.();
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error?.data?.error || 'Failed to save expense',
        variant: 'destructive',
      });
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader className="pb-4">
        <CardTitle className="text-xl font-semibold flex items-center gap-2">
          <Receipt className="h-5 w-5 text-blue-600" />
          {expense ? 'Edit Expense' : 'Create New Expense'}
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          {expense ? 'Update expense details' : 'Enter the required expense information'}
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="basic">Basic Info</TabsTrigger>
                <TabsTrigger value="relationships">Relations</TabsTrigger>
                <TabsTrigger value="financial">Financial</TabsTrigger>
                <TabsTrigger value="project">Project</TabsTrigger>
                <TabsTrigger value="approval">Approval</TabsTrigger>
              </TabsList>

              <TabsContent value="basic" className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Description <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter expense description"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Amount <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="0.00"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="expenseDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Date <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="vendorId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Vendor <span className="text-red-500">*</span>
                    </FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select vendor" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {vendors?.data?.map((vendor) => (
                          <SelectItem key={vendor.id} value={vendor.id}>
                            {vendor.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Category <span className="text-red-500">*</span>
                    </FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {expenseCategories?.data?.map((category) => (
                          <SelectItem key={category.id} value={category.id}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="paymentMethod"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Payment Method <span className="text-red-500">*</span>
                    </FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select payment method" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="cash">💵 Cash</SelectItem>
                        <SelectItem value="credit_card">💳 Credit Card</SelectItem>
                        <SelectItem value="debit_card">💳 Debit Card</SelectItem>
                        <SelectItem value="bank_transfer">🏦 Bank Transfer</SelectItem>
                        <SelectItem value="check">📝 Check</SelectItem>
                        <SelectItem value="online_payment">🌐 Online Payment</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="receiptUrl"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Receipt URL</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="https://example.com/receipt.pdf"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="notes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Notes</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Additional notes about this expense"
                            rows={3}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="isRecurring"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>
                          This is a recurring expense
                        </FormLabel>
                      </div>
                    </FormItem>
                  )}
                />
              </TabsContent>

              <TabsContent value="relationships" className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="accountId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Chart of Account</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select account" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="">No account</SelectItem>
                            {accounts?.data?.map((account) => (
                              <SelectItem key={account.id} value={account.id}>
                                {account.account_code} - {account.account_name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="assetId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Asset</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select asset" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="">No asset</SelectItem>
                            {assets?.assets?.map((asset) => (
                              <SelectItem key={asset.id} value={asset.id}>
                                {asset.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="employeeId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Employee</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select employee" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="">No employee</SelectItem>
                            {employees?.employees?.map((employee: any) => (
                              <SelectItem key={employee.id || employee.employee_id} value={employee.id || employee.employee_id}>
                                {employee.firstName && employee.lastName
                                  ? `${employee.firstName} ${employee.lastName}`
                                  : employee.employee_name || employee.fullName || 'Unknown Employee'
                                }
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="bankAccountId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Bank Account</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select bank account" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="">No bank account</SelectItem>
                            {bankAccounts?.accounts?.map((account) => (
                              <SelectItem key={account.id} value={account.id}>
                                {account.account_name} - {account.account_number}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="billId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Related Bill</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select bill" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="">No bill</SelectItem>
                            {bills?.bills?.map((bill) => (
                              <SelectItem key={bill.id} value={bill.id}>
                                {bill.billNumber || bill.bill_number || bill.id} - ${bill.totalAmount || bill.total_amount}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </TabsContent>

              <TabsContent value="financial" className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="subTotal"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Subtotal</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="0.00"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="taxAmount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tax Amount</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="0.00"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="discountAmount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Discount Amount</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="0.00"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </TabsContent>

              <TabsContent value="project" className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="projectId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Project ID</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter project ID"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="costCenter"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Cost Center</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter cost center"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="department"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Department</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter department"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="purchaseOrderId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Purchase Order ID</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter PO ID"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </TabsContent>

              <TabsContent value="approval" className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Status</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="PENDING">⏳ Pending</SelectItem>
                            <SelectItem value="APPROVED">✅ Approved</SelectItem>
                            <SelectItem value="REJECTED">❌ Rejected</SelectItem>
                            <SelectItem value="PAID">💰 Paid</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="approvedBy"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Approved By (User ID)</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter approver user ID"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="approvedAt"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Approved At</FormLabel>
                        <FormControl>
                          <Input
                            type="datetime-local"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="paidAt"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Paid At</FormLabel>
                        <FormControl>
                          <Input
                            type="datetime-local"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </TabsContent>
            </Tabs>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span>Saving...</span>
                </div>
              ) : (
                expense ? 'Update Expense' : 'Create Expense'
              )}
            </Button>
          </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

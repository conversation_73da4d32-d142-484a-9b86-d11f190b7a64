'use client';

import React, { useEffect } from 'react';
import { useForm, type SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { useToast } from '@/hooks/use-toast';
import CollapsibleSection from '@/components/CollapsibleSection';
import { Receipt } from 'lucide-react';

// API imports
import { useGetVendorsQuery } from '@/redux/services/vendorsApi';
import { useGetExpenseCategoriesByBranchQuery } from '@/redux/services/expenseCategoriesApi';
import { useCreateExpenseMutation, useUpdateExpenseMutation, type Expense } from '@/redux/services/expensesApi';

// Simplified schema focused on API requirements
const expenseSchema = z.object({
  // Required fields (matching CreateExpenseRequest)
  description: z.string().min(1, 'Description is required'),
  amount: z.number().min(0.01, 'Amount must be greater than 0'),
  expenseDate: z.string().min(1, 'Expense date is required'),
  vendorId: z.string().min(1, 'Vendor is required'),
  category: z.string().min(1, 'Category is required'),
  paymentMethod: z.enum(['cash', 'credit_card', 'debit_card', 'bank_transfer', 'check', 'online_payment'], {
    required_error: 'Payment method is required',
  }),

  // Optional fields
  receiptUrl: z.string().url('Invalid URL').optional().or(z.literal('')),
  notes: z.string().optional(),

  // Status (for editing)
  status: z.enum(['PENDING', 'APPROVED', 'REJECTED', 'PAID']).default('PENDING'),
});

type ExpenseFormData = z.infer<typeof expenseSchema>;

interface ExpenseFormProps {
  expense?: Expense;
  branchId: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function ExpenseForm({ expense, branchId, onSuccess, onCancel }: ExpenseFormProps) {
  const { toast } = useToast();

  // API queries - only what we actually need
  const { data: vendors } = useGetVendorsQuery();
  const { data: expenseCategories } = useGetExpenseCategoriesByBranchQuery(branchId);

  // Mutations
  const [createExpense, { isLoading: isCreating }] = useCreateExpenseMutation();
  const [updateExpense, { isLoading: isUpdating }] = useUpdateExpenseMutation();

  const isLoading = isCreating || isUpdating;

  const form = useForm<ExpenseFormData>({
    resolver: zodResolver(expenseSchema),
    mode: 'onChange',
    defaultValues: {
      description: '',
      amount: 0,
      expenseDate: new Date().toISOString().split('T')[0],
      vendorId: '',
      category: '',
      paymentMethod: 'cash' as const,
      receiptUrl: '',
      notes: '',
      status: 'PENDING' as const,
    },
  });

  // Watch form values for controlled components
  const formValues = form.watch();

  // Populate form when editing
  useEffect(() => {
    if (expense) {
      form.reset({
        description: expense.description,
        amount: expense.amount,
        expenseDate: expense.date.split('T')[0],
        vendorId: expense.vendor_id || '',
        category: expense.category || '',
        paymentMethod: (expense.payment_method as any) || 'cash',
        receiptUrl: expense.receipt_url || '',
        notes: '', // Note: Expense type doesn't have notes field
        status: expense.status,
      });
    }
  }, [expense, form]);

  const onSubmit: SubmitHandler<ExpenseFormData> = async (data) => {
    try {
      if (expense) {
        // For updates, we can send the full data
        const updateData = {
          ...data,
          date: data.expenseDate,
        };
        await updateExpense({ id: expense.id, body: updateData }).unwrap();
        toast({
          title: 'Success',
          description: 'Expense updated successfully',
        });
      } else {
        // For creation, match the CreateExpenseRequest interface
        const createData = {
          date: data.expenseDate,
          vendor_id: data.vendorId,
          category: data.category,
          description: data.description,
          amount: data.amount,
          payment_method: data.paymentMethod,
          receipt_url: data.receiptUrl || undefined,
        };
        await createExpense(createData).unwrap();
        toast({
          title: 'Success',
          description: 'Expense created successfully',
        });
      }

      onSuccess?.();
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error?.data?.error || 'Failed to save expense',
        variant: 'destructive',
      });
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader className="pb-4">
        <CardTitle className="text-xl font-semibold flex items-center gap-2">
          <Receipt className="h-5 w-5 text-blue-600" />
          {expense ? 'Edit Expense' : 'Create New Expense'}
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          {expense ? 'Update expense details' : 'Enter the required expense information'}
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Essential Fields - Always Visible */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="description" className="text-sm font-medium">
                Description <span className="text-red-500">*</span>
              </Label>
              <Input
                id="description"
                {...form.register('description')}
                placeholder="Enter expense description"
                className="w-full"
              />
              {form.formState.errors.description && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.description.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="amount" className="text-sm font-medium">
                Amount <span className="text-red-500">*</span>
              </Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                {...form.register('amount', { valueAsNumber: true })}
                placeholder="0.00"
                className="w-full"
              />
              {form.formState.errors.amount && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.amount.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="expenseDate" className="text-sm font-medium">
                Date <span className="text-red-500">*</span>
              </Label>
              <Input
                id="expenseDate"
                type="date"
                {...form.register('expenseDate')}
                className="w-full"
              />
              {form.formState.errors.expenseDate && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.expenseDate.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="vendorId" className="text-sm font-medium">
                Vendor <span className="text-red-500">*</span>
              </Label>
              <Select
                value={formValues.vendorId || ''}
                onValueChange={(value) => form.setValue('vendorId', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select vendor" />
                </SelectTrigger>
                <SelectContent>
                  {vendors?.data?.map((vendor) => (
                    <SelectItem key={vendor.id} value={vendor.id}>
                      {vendor.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {form.formState.errors.vendorId && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.vendorId.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="category" className="text-sm font-medium">
                Category <span className="text-red-500">*</span>
              </Label>
              <Select
                value={formValues.category || ''}
                onValueChange={(value) => form.setValue('category', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {expenseCategories?.data?.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {form.formState.errors.category && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.category.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="paymentMethod" className="text-sm font-medium">
                Payment Method <span className="text-red-500">*</span>
              </Label>
              <Select
                value={formValues.paymentMethod || ''}
                onValueChange={(value) => form.setValue('paymentMethod', value as any)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select payment method" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="cash">💵 Cash</SelectItem>
                  <SelectItem value="credit_card">💳 Credit Card</SelectItem>
                  <SelectItem value="debit_card">💳 Debit Card</SelectItem>
                  <SelectItem value="bank_transfer">🏦 Bank Transfer</SelectItem>
                  <SelectItem value="check">📝 Check</SelectItem>
                  <SelectItem value="online_payment">🌐 Online Payment</SelectItem>
                </SelectContent>
              </Select>
              {form.formState.errors.paymentMethod && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.paymentMethod.message}
                </p>
              )}
            </div>
          </div>

          {/* Optional Fields - Collapsible */}
          <CollapsibleSection
            title="Additional Details"
            defaultExpanded={false}
            className="border border-gray-200"
          >
            <div className="p-4 space-y-4">
              <div className="space-y-2">
                <Label htmlFor="receiptUrl" className="text-sm font-medium">
                  Receipt URL
                </Label>
                <Input
                  id="receiptUrl"
                  {...form.register('receiptUrl')}
                  placeholder="https://example.com/receipt.pdf"
                  className="w-full"
                />
                {form.formState.errors.receiptUrl && (
                  <p className="text-sm text-red-500">
                    {form.formState.errors.receiptUrl.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes" className="text-sm font-medium">
                  Notes
                </Label>
                <Textarea
                  id="notes"
                  {...form.register('notes')}
                  placeholder="Additional notes about this expense"
                  rows={3}
                  className="w-full"
                />
              </div>

              {expense && (
                <div className="space-y-2">
                  <Label htmlFor="status" className="text-sm font-medium">
                    Status
                  </Label>
                  <Select
                    value={formValues.status || 'PENDING'}
                    onValueChange={(value) => form.setValue('status', value as any)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="PENDING">⏳ Pending</SelectItem>
                      <SelectItem value="APPROVED">✅ Approved</SelectItem>
                      <SelectItem value="REJECTED">❌ Rejected</SelectItem>
                      <SelectItem value="PAID">💰 Paid</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>
          </CollapsibleSection>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span>Saving...</span>
                </div>
              ) : (
                expense ? 'Update Expense' : 'Create Expense'
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
